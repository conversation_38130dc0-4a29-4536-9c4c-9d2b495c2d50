// socket-profile5.0/indicators/data/perpImbalanceDataStore.js
// Global PerpImbalance data store with background updating and subscription API

const listeners = [];
let perpImbalanceData = null;
let isUpdating = false;
// let updateInterval = 1000; // ms, adjust as needed // Will be derived from PERP_IMBALANCE_CONFIG

// Configuration - use centralized config with fallback
const PERP_IMBALANCE_CONFIG = window.CONFIG?.perpImbalance || {
  updateIntervalMs: 1000,
  lookbackPeriod: 1440,
  colors: {
    positive: "#4CAF50",
    negative: "#F44336",
    neutral: "#9E9E9E",
  },
};
const updateInterval = PERP_IMBALANCE_CONFIG.updateIntervalMs;

// --- Real PerpImbalance calculation logic ---
// You must provide spotData, futuresData, oiData from your data source
// Access normalizeImbalance and calculateWanger via window
let spotData = [];
let futuresData = [];
let oiData = [];

// Namespace all global exports under window.PS
window.PS = window.PS || {};

window.PS.setPerpImbalanceSourceData = function ({ spot, futures, oi }) {
  if (spot) spotData = spot;
  if (futures) futuresData = futures;
  if (oi) oiData = oi;
  console.log("[PerpImbalance DataStore] setPerpImbalanceSourceData called:", {
    spotLength: spot?.length || 0,
    futuresLength: futures?.length || 0,
    oiLength: oi?.length || 0,
    listenersCount: listeners.length
  });
  // Skip automatic calculation - updateImbalance handles calculations directly
  // updatePerpImbalanceData();
};

// Implements setPerpImbalance to update perpImbalanceData and notify listeners
window.PS.setPerpImbalance = function (newImbalanceData) {
  perpImbalanceData = newImbalanceData;
  console.log("[PerpImbalance DataStore] setPerpImbalance called:", {
    time: newImbalanceData?.time,
    value: newImbalanceData?.value,
    listenersCount: listeners.length,
  });
  listeners.forEach((cb, index) => {
    try {
      console.log(
        `[PerpImbalance DataStore] Calling listener ${index + 1}/${listeners.length}`,
      );
      cb(perpImbalanceData);
    } catch (e) {
      console.error(
        `[PerpImbalance DataStore] Error in listener ${index + 1}:`,
        e,
      );
    }
  });
};

// Defensive stub: guarantee subscribePerpImbalance exists
if (!window.PS.subscribePerpImbalance) {
  window.PS.subscribePerpImbalance = function (cb) {
    console.log("subscribePerpImbalance stub called");
    return function () {};
  };
}

if (!window.PS.getCurrentPerpImbalance) {
  window.PS.getCurrentPerpImbalance = function () {
    console.log("getCurrentPerpImbalance stub called");
    return null;
  };
}

// Add periodic update mechanism to ensure data freshness
let perpImbalanceUpdateTimer = null;

function startPerpImbalancePeriodicUpdates() {
  if (perpImbalanceUpdateTimer) {
    clearInterval(perpImbalanceUpdateTimer);
  }

  perpImbalanceUpdateTimer = setInterval(() => {
    // Only update if we have data and listeners
    if ((spotData.length > 0 || futuresData.length > 0) && listeners.length > 0) {
      const now = Math.floor(Date.now() / 1000);
      const currentBarTime = Math.floor(now / 300) * 300; // 5-minute intervals

      // Always trigger an update every 30 seconds to ensure freshness
      console.log(`[PerpImbalance DataStore] Triggering periodic update at ${new Date().toISOString()}`);
      // Create a synthetic update with current time if we have historical data
      if (perpImbalanceData) {
        const syntheticData = {
          time: currentBarTime,
          value: perpImbalanceData.value || 0 // Use last known value
        };
        window.PS.setPerpImbalance(syntheticData);
      }
    }
  }, 30000); // Check every 30 seconds

  // Also add a more frequent check for bar close events
  const barCloseTimer = setInterval(() => {
    if ((spotData.length > 0 || futuresData.length > 0) && listeners.length > 0) {
      const now = Math.floor(Date.now() / 1000);
      const currentBarTime = Math.floor(now / 300) * 300; // 5-minute intervals
      const secondsIntoBar = now % 300;

      // Trigger update at the start of each new 5-minute bar (within first 10 seconds)
      if (secondsIntoBar <= 10 && perpImbalanceData) {
        console.log(`[PerpImbalance DataStore] Bar close update at ${new Date().toISOString()}`);
        const syntheticData = {
          time: currentBarTime,
          value: perpImbalanceData.value || 0
        };
        window.PS.setPerpImbalance(syntheticData);
      }
    }
  }, 10000); // Check every 10 seconds

  // Store both timers for cleanup
  perpImbalanceUpdateTimer.barCloseTimer = barCloseTimer;
}

// Initialize PS namespace first
window.PS = window.PS || {};

// Subscription API
window.PS.subscribePerpImbalance = function (cb) {
  if (typeof cb !== "function") return () => {};
  listeners.push(cb);
  console.log(
    "[PerpImbalance DataStore] New subscriber added. Total listeners:",
    listeners.length,
  );

  // Start periodic updates if this is the first listener
  if (listeners.length === 1) {
    startPerpImbalancePeriodicUpdates();
  }

  // Immediately send current data if available
  if (perpImbalanceData !== null) {
    console.log(
      "[PerpImbalance DataStore] Sending current data to new subscriber:",
      perpImbalanceData,
    );
    cb(perpImbalanceData);
  }
  // Return unsubscribe function
  return () => {
    const idx = listeners.indexOf(cb);
    if (idx !== -1) {
      listeners.splice(idx, 1);
      console.log(
        "[PerpImbalance DataStore] Subscriber removed. Remaining listeners:",
        listeners.length,
      );
    }
    // Stop periodic updates if no more listeners
    if (listeners.length === 0 && perpImbalanceUpdateTimer) {
      clearInterval(perpImbalanceUpdateTimer);
      if (perpImbalanceUpdateTimer.barCloseTimer) {
        clearInterval(perpImbalanceUpdateTimer.barCloseTimer);
      }
      perpImbalanceUpdateTimer = null;
    }
  };
};

window.PS.getCurrentPerpImbalance = function () {
  return perpImbalanceData;
};

window.PS.getCurrentPerpImbalanceListenerCount = function () {
  return listeners.length;
};

// Backward compatibility - expose at global level too
window.subscribePerpImbalance = window.PS.subscribePerpImbalance;
window.getCurrentPerpImbalance = window.PS.getCurrentPerpImbalance;
window.getCurrentPerpImbalanceListenerCount =
  window.PS.getCurrentPerpImbalanceListenerCount;

async function fetchOrCalculatePerpImbalance() {
  // Use the latest bar from each data source
  if (!spotData.length || !futuresData.length || !oiData.length) {
    return null;
  }
  const spotBar = spotData[spotData.length - 1];
  const futuresBar = futuresData[futuresData.length - 1];
  const oiBar = oiData[oiData.length - 1];

  // Calculate cumulative deltas
  const spotDelta = spotBar.volume * (spotBar.close - spotBar.open);
  const futuresDelta = futuresBar.volume * (futuresBar.close - futuresBar.open);

  // Maintain running CVDs
  if (!fetchOrCalculatePerpImbalance.spotCVD)
    fetchOrCalculatePerpImbalance.spotCVD = 0;
  if (!fetchOrCalculatePerpImbalance.futuresCVD)
    fetchOrCalculatePerpImbalance.futuresCVD = 0;
  fetchOrCalculatePerpImbalance.spotCVD += spotDelta;
  fetchOrCalculatePerpImbalance.futuresCVD += futuresDelta;

  const imbalanceValue =
    fetchOrCalculatePerpImbalance.futuresCVD -
    fetchOrCalculatePerpImbalance.spotCVD;
  const oiValue = oiBar.close;

  // Maintain history for normalization
  if (!fetchOrCalculatePerpImbalance.historicalImbalanceData)
    fetchOrCalculatePerpImbalance.historicalImbalanceData = [];
  if (!fetchOrCalculatePerpImbalance.historicalOIData)
    fetchOrCalculatePerpImbalance.historicalOIData = [];
  fetchOrCalculatePerpImbalance.historicalImbalanceData.push({
    time: spotBar.time,
    value: imbalanceValue,
  });
  fetchOrCalculatePerpImbalance.historicalOIData.push({
    time: spotBar.time,
    value: oiValue,
  });

  // Keep only the last N bars for normalization
  const lookbackPeriod = PERP_IMBALANCE_CONFIG.lookbackPeriod;
  if (
    fetchOrCalculatePerpImbalance.historicalImbalanceData.length >
    lookbackPeriod
  )
    fetchOrCalculatePerpImbalance.historicalImbalanceData =
      fetchOrCalculatePerpImbalance.historicalImbalanceData.slice(
        -lookbackPeriod,
      );
  if (fetchOrCalculatePerpImbalance.historicalOIData.length > lookbackPeriod)
    fetchOrCalculatePerpImbalance.historicalOIData =
      fetchOrCalculatePerpImbalance.historicalOIData.slice(-lookbackPeriod);

  // Normalize
  const lookbackData = fetchOrCalculatePerpImbalance.historicalImbalanceData;
  const oiLookbackData = fetchOrCalculatePerpImbalance.historicalOIData;
  const imbalanceMin = Math.min(...lookbackData.map((d) => d.value));
  const imbalanceMax = Math.max(...lookbackData.map((d) => d.value));
  const oiMin = Math.min(...oiLookbackData.map((d) => d.value));
  const oiMax = Math.max(...oiLookbackData.map((d) => d.value));
  const normalizedImbalance = window.normalizeImbalance(
    imbalanceValue,
    imbalanceMin,
    imbalanceMax,
  );

  return {
    time: spotBar.time,
    value: normalizedImbalance,
  };
}

async function updatePerpImbalanceData() {
  if (isUpdating) return;
  isUpdating = true;
  console.log("[PerpImbalance DataStore] Starting data update...");
  try {
    // Skip automatic calculation - updateImbalance handles this directly
    // const newData = await fetchOrCalculatePerpImbalance();
    // perpImbalanceData = newData;
    console.log(
      "[PerpImbalance DataStore] Data update skipped - using direct calculation",
    );
  } finally {
    isUpdating = false;
  }
}
